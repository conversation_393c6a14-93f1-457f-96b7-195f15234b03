import {
  Box,
  Button,
  Flex,
  IconButton,
  Text,
  Textarea,
  useToast,
  Image,
  CloseButton,
  Center,
  VStack,
} from '@chakra-ui/react';
import React, { useRef, useState, useEffect } from 'react';
import { useMutation, useQuery } from 'react-query';
import { useDispatch, useSelector } from 'react-redux';
import { useSearchParams } from 'react-router-dom';
import useFileValidation from '../../../../hooks/useFileValidation';
import {
  MessagesService,
  SendMessageDto,
} from '../../../../services/messages.service';
import {
  addMessagesToConversation,
  getMessageByTempIdAndConversationId,
  getMessagesByConversationId,
  updateMessageByTempId,
  updateMessageStatusByTempId,
  updateMessageUploadProgressByTempId,
} from '../../../../state/inboxSlice';
import { AppDispatch, store, RootState } from '../../../../state/store';
import { v4 as uuidv4 } from 'uuid';
import { FileValidationUtils } from '../../../../utils/file-validation.utils';
import { FaPaperclip, FaTimes, FaFileUpload } from 'react-icons/fa';
import { colors } from '../../../../constants/colors';
import { useSendTemplateModal } from '../../../../hooks/useSendTemplateModal';
import { ConversationWithIncludes } from '../../../../types/Conversation';
import { apiRoutes } from '../../../../constants/api-routes';
import { QuickRepliesService } from '../../../../services/quick-replies.service';
import InputButtons from './InputButtons';
import { BsFillEmojiSmileFill } from 'react-icons/bs';
import CustomEmojiPicker from '../../../../components/CustomEmojiPicker';
import MessageItem from '../../../../components/MessageItem';
import { MessageWithCardsIncludes } from '../../../../types/Message';
import AIButton from '../../../../components/AIButton';
import AIReplySuggestions from './AIReplySuggestions';
import { AgentsService } from '../../../../services/agents.service';
import {
  CreateInfoLogDto,
  LogsService,
} from '../../../../services/logs.service';
import { LogType } from '../../../../types/Prisma';
const ALLOWED_COMPANIES_TO_TEST =
  process.env.REACT_APP_ALLOWED_COMPANIES_TO_TEST || '';
import { GupshupTemplateType } from '../../../../types/GupshupTemplateType';
import { MdQuickreply } from 'react-icons/md';

interface InputAreaProps {
  conversation: ConversationWithIncludes;
  isDesktop: boolean;
  contextMessage?: MessageWithCardsIncludes | null;
  onClearContextMessage?: () => void;
}

interface FilePreview {
  file: File;
  previewUrl?: string;
}

const InputArea = ({
  conversation,
  isDesktop,
  contextMessage,
  onClearContextMessage,
}: InputAreaProps) => {
  const [searchParams] = useSearchParams();
  const { openModal: openSendTemplateModal } = useSendTemplateModal();
  const { currentUser } = useSelector((state: RootState) => state.auth);

  // Get messages for this conversation to detect new user messages
  const messages = useSelector((state: RootState) =>
    getMessagesByConversationId(state, conversation?.id || ''),
  );

  const { data: quickReplies } = useQuery(
    apiRoutes.listQuickReplies(),
    async () => {
      const { data } = await QuickRepliesService.listQuickReplies();
      return data;
    },
    {
      staleTime: 1000 * 60 * 10,
    },
  );

  const aiReplySuggestionsMutation = useMutation(
    async (conversationId: string): Promise<string> => {
      if (
        !conversationId ||
        ALLOWED_COMPANIES_TO_TEST === '' ||
        !ALLOWED_COMPANIES_TO_TEST.split(',').includes(
          conversation?.companyId || '',
        )
      ) {
        return '';
      }

      const { data } = await AgentsService.getAIReplySuggestions(
        conversation?.id,
      );
      return Array.isArray(data.suggestions)
        ? data.suggestions.length > 0
          ? data.suggestions[0]
          : ''
        : data.suggestions;
    },
    {
      onSuccess: (data: string) => {
        const currentText = textareaRef.current?.value || '';
        if (data.length > 0 && currentText === '') {
          setAutoCompleteSuggestion(data);
          setShowAutoComplete(true);
        }

        if (data.length > 0) {
          createAiAgentSuggestionLog(
            'generateAiAgentSuggestion',
            'Sugestões de resposta geradas pelo agente de IA na conversa',
            { suggestions: data },
          );
        }
      },
      onError: (error) => {},
    },
  );

  async function createAiAgentSuggestionLog(
    type: LogType,
    message: string,
    data: Record<string, any>,
  ) {
    LogsService.createInfoLog({
      type,
      message,
      source: 'internal',
      meta: {
        conversationId: conversation?.id,
        userId: currentUser?.sub,
        ...data,
      },
    } as CreateInfoLogDto);
  }

  const [showQuickReplies, setShowQuickReplies] = useState(false);
  const fileInputRef = useRef(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const lastProcessedMessageRef = useRef<string | null>(null);
  const conversationId = searchParams.get('conversationId');
  const [filePreview, setFilePreview] = useState<FilePreview | null>(null);
  const dispatch = useDispatch<AppDispatch>();
  const { validateFile, getAcceptedMessageTemplateFileTypes } =
    useFileValidation();
  const [text, setText] = useState('');
  const [isDraggingFile, setIsDraggingFile] = useState(false);

  // Auto complete states
  const [autoCompleteSuggestion, setAutoCompleteSuggestion] =
    useState<string>('');
  const [showAutoComplete, setShowAutoComplete] = useState(false);
  const [displayedSuggestion, setDisplayedSuggestion] = useState<string>('');
  const [isTypingAnimation, setIsTypingAnimation] = useState(false);

  // Textarea focus state for animated border
  const [isTextareaFocused, setIsTextareaFocused] = useState(false);

  useEffect(() => {
    setText('');
    // Reset the last processed message when conversation changes
    lastProcessedMessageRef.current = null;
    // Reset auto complete states
    setAutoCompleteSuggestion('');
    setDisplayedSuggestion('');
    setShowAutoComplete(false);
    setIsTypingAnimation(false);
  }, [conversationId]);

  // Effect to animate typing when a new suggestion is set
  useEffect(() => {
    console.log('Animation effect triggered:', {
      autoCompleteSuggestion,
      showAutoComplete,
    });

    if (!autoCompleteSuggestion || !showAutoComplete) {
      setDisplayedSuggestion('');
      setIsTypingAnimation(false);
      return;
    }

    console.log('Starting typing animation for:', autoCompleteSuggestion);
    setIsTypingAnimation(true);
    setDisplayedSuggestion('');

    const totalDuration = 500; // 300ms total
    const charactersPerStep = Math.max(
      1,
      Math.ceil(autoCompleteSuggestion.length / 10),
    ); // Show multiple chars per step for smoother animation
    const stepDuration =
      totalDuration /
      Math.ceil(autoCompleteSuggestion.length / charactersPerStep);

    let currentIndex = 0;

    const typeInterval = setInterval(() => {
      currentIndex += charactersPerStep;

      if (currentIndex >= autoCompleteSuggestion.length) {
        setDisplayedSuggestion(autoCompleteSuggestion);
        setIsTypingAnimation(false);
        clearInterval(typeInterval);
        console.log('Typing animation completed');
      } else {
        setDisplayedSuggestion(
          autoCompleteSuggestion.substring(0, currentIndex),
        );
      }
    }, stepDuration);

    return () => {
      clearInterval(typeInterval);
      setIsTypingAnimation(false);
    };
  }, [autoCompleteSuggestion, showAutoComplete]);

  // Effect to detect new user messages and trigger AI suggestions
  useEffect(() => {
    if (!conversation?.id || !messages.length) return;

    // Get the most recent message
    const lastMessage = messages[0];

    // Check if it's a new message from user (not from system) and chat is open
    if (
      lastMessage &&
      !lastMessage.fromSystem &&
      text === '' &&
      !aiReplySuggestionsMutation.isLoading &&
      !showAutoComplete &&
      lastProcessedMessageRef.current !== lastMessage.id &&
      conversation?.id === conversationId
    ) {
      lastProcessedMessageRef.current = lastMessage.id;

      setTimeout(() => {
        aiReplySuggestionsMutation.mutate(conversation.id);
      }, 500);
    }
  }, [
    messages,
    conversation?.id,
    text,
    aiReplySuggestionsMutation.isLoading,
    showAutoComplete,
  ]);

  function triggerAutoCompleteOnNewMessage() {
    console.log('Trigger auto complete clicked', {
      text,
      showAutoComplete,
      conversation: conversation?.id,
    });

    if (text === '' && !showAutoComplete) {
      setShowAutoComplete(true);
      setAutoCompleteSuggestion(
        'Olá! Recomendo o nosso Power Focus Cacau Display com 14 sachês individuais de 140g, que é uma ótima opção econômica. Ele está disponível por R$ 86,80 e é 100% natural, sem conservantes. Você quer mais informações sobre ele ou prefere outras sugestões?',
      );
      console.log('Starting AI mutation...');
    } else {
      console.log('Conditions not met:', {
        textEmpty: text === '',
        autoCompleteNotShowing: !showAutoComplete,
      });
    }
  }

  function updateUploadProgress(tempId: string, uploadProgress: number) {
    dispatch(
      updateMessageUploadProgressByTempId({
        conversationId: conversationId!,
        tempId,
        uploadProgress,
      }),
    );
  }

  const sendMessage = useMutation(
    async (sendMessageDto: SendMessageDto) => {
      const { data } = await MessagesService.sendMessage(sendMessageDto);
      return data;
    },
    {
      onMutate: async (sendMessageDto) => {
        dispatch(
          addMessagesToConversation({
            conversationId: conversationId!,
            messages: [
              {
                id: '',
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString(),
                tempId: sendMessageDto.tempId,
                text: sendMessageDto.text,
                senderPhoneNumberId: '',
                recipientPhoneNumberId: '',
                conversationId: conversationId!,
                fromSystem: true,
                status: 'enqueued',
                whatsappMessageId: null,
                mediaId: null,
                mediaUrl: null,
                mediaType: null,
                fileKey: null,
                messageTemplateId: null,
                whatsappCampaignId: null,
                firstReplyId: null,
                errorCode: null,
                errorMessage: null,
                automationId: null,
                uploadProgress: sendMessageDto.file ? 1 : 0,
                flowNodeId: null,
                contextMessageId: sendMessageDto.contextMessageId,
                context: { ...contextMessage },
              },
            ],
          }),
        );

        validateAiAgentSuggestionWasUsedAndCreateLog();
        setText('');
        setAutoCompleteSuggestion('');
        setShowAutoComplete(false);
        if (filePreview?.previewUrl) {
          URL.revokeObjectURL(filePreview.previewUrl);
        }
        setFilePreview(null);
      },
      onError: (error: any, sendMessageDto) => {
        const message = getMessageByTempIdAndConversationId(
          store.getState(),
          conversationId!,
          sendMessageDto.tempId,
        );
        if (
          [504, 0].includes(error?.response?.status) &&
          message?.uploadProgress === 100
        ) {
          return;
        }
        dispatch(
          updateMessageStatusByTempId({
            conversationId: conversationId!,
            tempId: sendMessageDto.tempId,
            status: 'failed',
          }),
        );
        updateUploadProgress(sendMessageDto.tempId, 0);
      },
      onSuccess: (data, sendMessageDto) => {
        dispatch(
          updateMessageByTempId({
            conversationId: conversationId!,
            tempId: sendMessageDto.tempId,
            message: { ...data },
          }),
        );
      },
    },
  );

  function phraseSimilarityPercentage(
    original: string,
    modified: string,
  ): number {
    const normalize = (str: string) =>
      str
        .toLowerCase()
        .replace(/[.,!?]/g, '')
        .split(/\s+/);

    const originalWords = normalize(original);
    const modifiedWords = normalize(modified);

    let matchCount = 0;
    for (const word of originalWords) {
      if (modifiedWords.includes(word)) {
        matchCount++;
      }
    }

    return matchCount / originalWords.length;
  }

  function validateAiAgentSuggestionWasUsedAndCreateLog() {
    if (autoCompleteSuggestion.length > 0) {
      const percentageOfSuggestionUsed = phraseSimilarityPercentage(
        autoCompleteSuggestion,
        text,
      );
      if (percentageOfSuggestionUsed < 0.1) {
        return;
      }
      createAiAgentSuggestionLog(
        'sendAiAgentSuggestion',
        'Resposta enviada ao cliente pelo agente humano gerada pelo agente de IA na conversa',
        {
          suggestion: autoCompleteSuggestion,
          messageSent: text,
          percentageOfSuggestionUsed,
        },
      );
    }
  }

  function handleSubmitMessage(e: React.FormEvent<HTMLFormElement>) {
    e.preventDefault();

    if (text.trim() === '' && !filePreview) return;

    onClearContextMessage?.();

    if (filePreview) {
      const tempId = uuidv4();
      sendMessage.mutate({
        text: text.trim() || filePreview.file.name,
        conversationId: conversationId!,
        tempId: tempId,
        contextMessageId: contextMessage?.id,
        file: filePreview.file,
        onProgress: (progress: number) => {
          updateUploadProgress(tempId, progress);
        },
      });
    } else {
      sendMessage.mutate({
        text,
        conversationId: conversationId!,
        tempId: uuidv4(),
        contextMessageId: contextMessage?.id,
      });
    }
  }

  function buildInputButtonGroup() {
    return (
      <InputButtons
        currentText={text}
        onSend={handleSendAudio}
        isDesktop={isDesktop}
        fileAttached={!!filePreview}
      />
    );
  }

  function handleTextareaKeyDown(e: React.KeyboardEvent<HTMLTextAreaElement>) {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmitMessage(e as any);
    }

    // Handle Tab key for auto complete
    if (
      e.key === 'Tab' &&
      showAutoComplete &&
      autoCompleteSuggestion &&
      text === ''
    ) {
      e.preventDefault();
      setText(autoCompleteSuggestion); // Use the full suggestion, not just displayed part
      setShowAutoComplete(false);
      setDisplayedSuggestion('');
      setIsTypingAnimation(false);
    }
  }

  function handleTextareaFocus() {
    setIsTextareaFocused(true);
  }

  function handleTextareaBlur() {
    setIsTextareaFocused(false);
  }

  function handleFileSelect(file: File) {
    const isValidFile = validateFile(file);
    if (isValidFile) {
      const previewUrl = file.type.startsWith('image/')
        ? URL.createObjectURL(file)
        : undefined;

      setFilePreview({ file, previewUrl });
    }
  }

  function handleFileChange(event: any) {
    const file = event.target.files && event.target.files[0];
    if (file) {
      handleFileSelect(file);
    }
    event.target.value = null;
  }

  function handleSendAudio(file: File) {
    const isValidFile = validateFile(file);
    if (isValidFile) {
      sendMessage.mutate({
        text: file.name,
        conversationId: conversationId!,
        tempId: uuidv4(),
        contextMessageId: contextMessage?.id,
        file,
      });
    }
    return true;
  }

  function handleEmojiPick(emoji: string) {
    const textarea = textareaRef.current! as any;
    const startPosition = textarea.selectionStart;
    const endPosition = textarea.selectionEnd;
    const currentValue = textarea.value;

    const newValue =
      currentValue.substring(0, startPosition) +
      emoji +
      currentValue.substring(endPosition);

    setText(newValue);
    textarea.focus();
    textarea.setSelectionRange(
      startPosition + emoji.length + 1,
      startPosition + emoji.length + 1,
    );
  }

  function handleClearFilePreview() {
    if (filePreview?.previewUrl) {
      URL.revokeObjectURL(filePreview.previewUrl);
    }
    setFilePreview(null);
  }

  function handleChangeTextarea(e: React.ChangeEvent<HTMLTextAreaElement>) {
    const text = e.target.value;
    if (text === '') {
      setShowQuickReplies(false);
      // If there's a suggestion available and field becomes empty, show it again
      if (autoCompleteSuggestion && !showAutoComplete) {
        setShowAutoComplete(true);
      }
    } else {
      // Hide auto complete when user starts typing
      setShowAutoComplete(false);
    }
    setText(text);
  }

  function handleDragEnter(e: React.DragEvent) {
    e.preventDefault();
    e.stopPropagation();
    setIsDraggingFile(true);
  }

  function handleDragLeave(e: React.DragEvent) {
    e.preventDefault();
    e.stopPropagation();

    if (e.currentTarget.contains(e.relatedTarget as Node)) {
      return;
    }

    setIsDraggingFile(false);
  }

  function handleDragOver(e: React.DragEvent) {
    e.preventDefault();
    e.stopPropagation();
  }

  function handleDrop(e: React.DragEvent) {
    e.preventDefault();
    e.stopPropagation();
    setIsDraggingFile(false);

    const file = e.dataTransfer.files[0];
    if (file) {
      handleFileSelect(file);
    }
  }

  const getSupportedFileFormats = () => {
    const formats = [];

    const documentFormats = getAcceptedMessageTemplateFileTypes(
      GupshupTemplateType.DOCUMENT,
    );
    const imageFormats = getAcceptedMessageTemplateFileTypes(
      GupshupTemplateType.IMAGE,
    );
    const videoFormats = getAcceptedMessageTemplateFileTypes(
      GupshupTemplateType.VIDEO,
    );
    const audioFormats = getAcceptedMessageTemplateFileTypes(
      GupshupTemplateType.AUDIO,
    );

    if (documentFormats) formats.push('PDF');
    if (imageFormats) formats.push('Imagens (JPG, PNG)');
    if (videoFormats) formats.push('Vídeos (MP4, 3GP)');
    if (audioFormats) formats.push('Áudios (OGG, MP3)');

    return formats;
  };

  const isReplyingToMessage = !!contextMessage;

  return (
    <Box
      shadow={'md'}
      borderRadius="md"
      position="relative"
      onDragEnter={handleDragEnter}
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      onDrop={handleDrop}
    >
      {isDraggingFile && (
        <Box
          position="absolute"
          top="0"
          left="0"
          right="0"
          bottom="0"
          zIndex="10"
          bg="white"
          borderRadius="md"
          border="2px dashed"
          borderColor="blue.400"
        >
          <Center h="100%">
            <VStack spacing={3}>
              <FaFileUpload size={40} color={colors.primaryLight} />
              <Text fontWeight="bold" color={colors.primary} fontSize="lg">
                Arraste e solte aqui seu arquivo para enviar
              </Text>
              <Text fontSize="sm" color="gray.600">
                Formatos suportados: {getSupportedFileFormats().join(', ')}
              </Text>
            </VStack>
          </Center>
        </Box>
      )}

      {isReplyingToMessage && !sendMessage.isLoading && (
        <Box mb={0.5} position="relative">
          <MessageItem
            messageId={contextMessage.id}
            isContextMessage
            status={contextMessage.status || 'sent'}
            text={contextMessage.text || ''}
            bgColor={
              contextMessage.fromSystem ? colors.blueLight : colors.lightGrey
            }
            textColor={colors.black}
            createdAt={contextMessage.createdAt || '0'}
            isFromSystem={!!contextMessage.fromSystem}
          />
          <Box
            position="absolute"
            top={2}
            right={2}
            cursor="pointer"
            onClick={onClearContextMessage}
          >
            <FaTimes size={10} opacity={0.8} />
          </Box>
        </Box>
      )}

      {filePreview && (
        <Box p={3} mb={3} bg="gray.50" borderRadius="md" position="relative">
          <Flex align="center" gap={3}>
            {filePreview.previewUrl ? (
              <Image
                src={filePreview.previewUrl}
                alt="Preview"
                boxSize="50px"
                objectFit="cover"
                borderRadius="md"
              />
            ) : (
              <Box
                bg="gray.200"
                w="50px"
                h="50px"
                borderRadius="md"
                display="flex"
                alignItems="center"
                justifyContent="center"
              >
                <FaPaperclip />
              </Box>
            )}
            <Text fontSize="sm" color="gray.700">
              {filePreview.file.name}
            </Text>
          </Flex>
          <CloseButton
            size="sm"
            position="absolute"
            top={2}
            right={2}
            onClick={handleClearFilePreview}
          />
        </Box>
      )}

      {(showQuickReplies || text.startsWith('/')) && (
        <Flex
          flexDir="column"
          gap={2}
          position="absolute"
          bottom="100%"
          padding="10px"
          width="100%"
          overflow="scroll"
          maxHeight="60vh"
          boxShadow="inner"
          bg="white"
          zIndex={5}
        >
          {quickReplies
            ?.filter((reply) =>
              reply.title
                .toLowerCase()
                .includes(text.toLowerCase().replace('/', '')),
            )
            .map((reply) => (
              <Box
                boxShadow={'md'}
                padding={2}
                key={reply.id}
                fontSize={12}
                cursor="pointer"
                bgColor="white"
                onClick={() => {
                  setShowQuickReplies(false);
                  setText(reply.text);
                }}
                _hover={{ bgColor: colors.lightGrey }}
              >
                <Text fontWeight={'bold'}>{reply.title}</Text>
                <Text>{reply.text}</Text>
              </Box>
            ))}
        </Flex>
      )}

      <form onSubmit={handleSubmitMessage}>
        <Box position="relative">
          {!filePreview && (
            <Box position="relative">
              {showAutoComplete && displayedSuggestion && text === '' && (
                <Box
                  position="absolute"
                  top="-28px"
                  left="16px"
                  zIndex={10}
                  pointerEvents="none"
                >
                  <Box
                    position="relative"
                    bg="white"
                    px={2}
                    py={1}
                    borderRadius="md"
                    overflow="hidden"
                    sx={{
                      '&::before': {
                        content: '""',
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        right: 0,
                        bottom: 0,
                        borderRadius: 'md',
                        padding: '2px',
                        background:
                          'linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57, #ff9ff3, #54a0ff)',
                        backgroundSize: '400% 400%',
                        mask: 'linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0)',
                        maskComposite: 'xor',
                        WebkitMask:
                          'linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0)',
                        WebkitMaskComposite: 'xor',
                        animation: 'aiGradientBorder 3s ease-in-out infinite',
                      },
                      '@keyframes aiGradientBorder': {
                        '0%': {
                          backgroundPosition: '0% 50%',
                          opacity: 1,
                        },
                        '33.33%': {
                          backgroundPosition: '100% 50%',
                          opacity: 1,
                        },
                        '66.66%': {
                          backgroundPosition: '0% 50%',
                          opacity: 1,
                        },
                        '90%': {
                          backgroundPosition: '100% 50%',
                          opacity: 1,
                        },
                        '100%': {
                          backgroundPosition: '0% 50%',
                          opacity: 1,
                        },
                      },
                    }}
                  >
                    <Text
                      fontSize="xs"
                      color="gray.500"
                      fontWeight="medium"
                      position="relative"
                      zIndex={1}
                    >
                      Pressione Tab ⇥ para utilizar a sugestão
                    </Text>
                  </Box>
                </Box>
              )}

              {/* Suggestion text overlay with typing animation */}
              {showAutoComplete && displayedSuggestion && text === '' && (
                <Text
                  position="absolute"
                  top="8px"
                  left="16px"
                  fontSize="md"
                  pointerEvents="none"
                  zIndex={1}
                  whiteSpace="pre-wrap"
                  sx={{
                    color: 'gray.400',
                    // Only show blinking animation when not typing
                    animation: !isTypingAnimation
                      ? 'blinkSuggestion 2s ease-in-out infinite'
                      : 'none',
                    '@keyframes blinkSuggestion': {
                      '0%, 100%': {
                        color: 'gray.300',
                      },
                      '50%': {
                        color: 'gray.500',
                      },
                    },
                  }}
                >
                  {displayedSuggestion}
                  {isTypingAnimation && (
                    <Box
                      as="span"
                      sx={{
                        animation: 'typingCursor 1s ease-in-out infinite',
                        '@keyframes typingCursor': {
                          '0%, 50%': {
                            opacity: 1,
                          },
                          '51%, 100%': {
                            opacity: 0,
                          },
                        },
                      }}
                    >
                      |
                    </Box>
                  )}
                </Text>
              )}

              <Box
                position="relative"
                borderRadius="md"
                overflow="hidden"
                sx={
                  showAutoComplete
                    ? {
                        '&::before': {
                          content: '""',
                          position: 'absolute',
                          top: 0,
                          left: 0,
                          right: 0,
                          bottom: 0,
                          borderRadius: 'md',
                          padding: '2px',
                          background:
                            'linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57, #ff9ff3, #54a0ff)',
                          backgroundSize: '400% 400%',
                          mask: 'linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0)',
                          maskComposite: 'xor',
                          WebkitMask:
                            'linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0)',
                          WebkitMaskComposite: 'xor',
                          animation:
                            'aiTextareaBorder 3s ease-in-out 1 forwards',
                        },
                        '@keyframes aiTextareaBorder': {
                          '0%': {
                            backgroundPosition: '0% 50%',
                            opacity: 1,
                          },
                          '25%': {
                            backgroundPosition: '100% 50%',
                            opacity: 1,
                          },
                          '50%': {
                            backgroundPosition: '100% 100%',
                            opacity: 1,
                          },
                          '75%': {
                            backgroundPosition: '0% 100%',
                            opacity: 1,
                          },
                          '100%': {
                            backgroundPosition: '0% 50%',
                            opacity: 0,
                          },
                        },
                      }
                    : {}
                }
              >
                <Textarea
                  ref={textareaRef}
                  sx={{
                    '&:focus': {
                      outline: 'none',
                      boxShadow: 'none',
                      borderColor: 'transparent',
                      backgroundColor: 'transparent',
                    },
                  }}
                  border="none"
                  pr="4.5rem"
                  position="relative"
                  zIndex={2}
                  bg={
                    showAutoComplete && displayedSuggestion && text === ''
                      ? 'transparent'
                      : 'white'
                  }
                  onPasteCapture={(e) => {
                    if (e.clipboardData.files.length > 0) {
                      handleFileSelect(e.clipboardData.files[0]);
                    }
                  }}
                  onDragEnter={handleDragEnter}
                  onFocus={handleTextareaFocus}
                  onBlur={handleTextareaBlur}
                  placeholder={
                    showAutoComplete && displayedSuggestion && text === ''
                      ? ''
                      : 'Digite uma mensagem'
                  }
                  value={text}
                  onChange={handleChangeTextarea}
                  onKeyDown={handleTextareaKeyDown}
                  resize={text.includes('\n') ? 'vertical' : 'none'}
                  mb={2}
                />
              </Box>
            </Box>
          )}
        </Box>
        <Flex justifyContent={'space-between'}>
          <Box position="relative">
            <input
              style={{ display: 'none' }}
              ref={fileInputRef}
              type="file"
              onChange={handleFileChange}
              accept={[
                getAcceptedMessageTemplateFileTypes(
                  GupshupTemplateType.DOCUMENT,
                ),
                getAcceptedMessageTemplateFileTypes(GupshupTemplateType.IMAGE),
                getAcceptedMessageTemplateFileTypes(GupshupTemplateType.VIDEO),
                getAcceptedMessageTemplateFileTypes(GupshupTemplateType.AUDIO),
              ]
                .filter(Boolean)
                .join(', ')}
            />
            <IconButton
              aria-label="Anexar arquivo"
              variant="ghost"
              icon={<FaPaperclip fontSize={16} color={colors.darkGrey} />}
              onClick={() => {
                if (fileInputRef.current) {
                  // @ts-ignore
                  fileInputRef.current.click();
                }
              }}
            />
            <CustomEmojiPicker
              Icon={
                <BsFillEmojiSmileFill fontSize={16} color={colors.darkGrey} />
              }
              renderAs="IconButton"
              emojiPickerAnchorPosition="top"
              emojiPickerGap={10}
              onEmojiSelection={handleEmojiPick}
            />
            {isDesktop ? (
              <Button
                variant="ghost"
                color={colors.darkGrey}
                onClick={() => setShowQuickReplies((prev) => !prev)}
              >
                Respostas rápidas
              </Button>
            ) : (
              <IconButton
                icon={<MdQuickreply />}
                color={colors.darkGrey}
                bgColor={colors.white}
                onClick={() => setShowQuickReplies((prev) => !prev)}
                aria-label="Respostas rápidas"
              />
            )}
            <Button
              variant="ghost"
              color={colors.darkGrey}
              onClick={() =>
                openSendTemplateModal({
                  conversation: conversation!,
                  templateTypes: [
                    'INITIAL_MESSAGE',
                    'MARKETING',
                    'REVIEW_REQUEST',
                  ],
                  deliveryType: 'direct',
                })
              }
            >
              Templates Meta
            </Button>
            <Button
              variant="ghost"
              color={colors.darkGrey}
              onClick={triggerAutoCompleteOnNewMessage}
              size="sm"
            >
              Testar Auto Complete
            </Button>
          </Box>
          {buildInputButtonGroup()}
        </Flex>
      </form>
    </Box>
  );
};

export default InputArea;
